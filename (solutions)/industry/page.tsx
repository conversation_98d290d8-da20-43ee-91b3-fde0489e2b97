export default function IndustrySolutionsPage() {
  return (
    <div className="flex flex-col gap-5">
      <h1 className="text-3xl font-bold text-gray-800">Industry Energy Solutions</h1>
      <p className="text-gray-600">Tailored energy optimization solutions for industrial clients</p>
      
      <div className="grid gap-6 md:grid-cols-3">
        <div className="md:col-span-2">
          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <h2 className="text-xl font-medium text-gray-800 mb-4">Our Industrial Expertise</h2>
            <p className="text-gray-600 mb-4">
              GETEC specializes in delivering comprehensive energy solutions that help industrial clients 
              reduce costs, improve efficiency, and meet sustainability targets. Our solutions are designed
              to address the unique challenges faced by different industrial sectors.
            </p>
            
            <div className="aspect-video bg-gray-100 rounded-md mb-6 flex items-center justify-center">
              <p className="text-gray-400">Industry Solutions Overview Video</p>
            </div>
            
            <h3 className="text-lg font-medium text-gray-800 mb-3">Key Benefits</h3>
            <div className="grid gap-4 sm:grid-cols-2 mb-6">
              <div className="flex items-start">
                <div className="flex-shrink-0 h-6 w-6 bg-green-100 rounded-full flex items-center justify-center">
                  <svg className="h-4 w-4 text-green-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <div className="ml-3">
                  <h4 className="text-sm font-medium text-gray-800">Cost Reduction</h4>
                  <p className="text-sm text-gray-500">Lower operational energy expenses by 15-30%</p>
                </div>
              </div>
              
              <div className="flex items-start">
                <div className="flex-shrink-0 h-6 w-6 bg-green-100 rounded-full flex items-center justify-center">
                  <svg className="h-4 w-4 text-green-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <div className="ml-3">
                  <h4 className="text-sm font-medium text-gray-800">Increased Efficiency</h4>
                  <p className="text-sm text-gray-500">Optimize energy consumption with smart systems</p>
                </div>
              </div>
              
              <div className="flex items-start">
                <div className="flex-shrink-0 h-6 w-6 bg-green-100 rounded-full flex items-center justify-center">
                  <svg className="h-4 w-4 text-green-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <div className="ml-3">
                  <h4 className="text-sm font-medium text-gray-800">Sustainability</h4>
                  <p className="text-sm text-gray-500">Reduce carbon footprint and meet ESG goals</p>
                </div>
              </div>
              
              <div className="flex items-start">
                <div className="flex-shrink-0 h-6 w-6 bg-green-100 rounded-full flex items-center justify-center">
                  <svg className="h-4 w-4 text-green-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <div className="ml-3">
                  <h4 className="text-sm font-medium text-gray-800">Regulatory Compliance</h4>
                  <p className="text-sm text-gray-500">Stay ahead of energy regulations and requirements</p>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div>
          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200 mb-6">
            <h2 className="text-xl font-medium text-gray-800 mb-4">Industry Sectors</h2>
            <ul className="space-y-3">
              <li className="flex items-center">
                <div className="w-1.5 h-1.5 bg-green-600 rounded-full mr-2"></div>
                <span className="text-gray-700">Automotive Manufacturing</span>
              </li>
              <li className="flex items-center">
                <div className="w-1.5 h-1.5 bg-green-600 rounded-full mr-2"></div>
                <span className="text-gray-700">Chemical Processing</span>
              </li>
              <li className="flex items-center">
                <div className="w-1.5 h-1.5 bg-green-600 rounded-full mr-2"></div>
                <span className="text-gray-700">Food & Beverage</span>
              </li>
              <li className="flex items-center">
                <div className="w-1.5 h-1.5 bg-green-600 rounded-full mr-2"></div>
                <span className="text-gray-700">Pharmaceuticals</span>
              </li>
              <li className="flex items-center">
                <div className="w-1.5 h-1.5 bg-green-600 rounded-full mr-2"></div>
                <span className="text-gray-700">Steel & Metals</span>
              </li>
              <li className="flex items-center">
                <div className="w-1.5 h-1.5 bg-green-600 rounded-full mr-2"></div>
                <span className="text-gray-700">Paper & Pulp</span>
              </li>
              <li className="flex items-center">
                <div className="w-1.5 h-1.5 bg-green-600 rounded-full mr-2"></div>
                <span className="text-gray-700">Electronics Manufacturing</span>
              </li>
            </ul>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <h2 className="text-xl font-medium text-gray-800 mb-4">Resources</h2>
            <div className="space-y-4">
              <a href="#" className="flex items-center p-3 border border-gray-200 rounded-md hover:bg-gray-50">
                <svg className="w-5 h-5 text-gray-400 mr-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <div>
                  <h3 className="text-sm font-medium text-gray-800">Case Studies</h3>
                  <p className="text-xs text-gray-500">Success stories from industrial clients</p>
                </div>
              </a>
              
              <a href="#" className="flex items-center p-3 border border-gray-200 rounded-md hover:bg-gray-50">
                <svg className="w-5 h-5 text-gray-400 mr-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                <div>
                  <h3 className="text-sm font-medium text-gray-800">ROI Calculator</h3>
                  <p className="text-xs text-gray-500">Estimate potential savings for your industry</p>
                </div>
              </a>
              
              <a href="#" className="flex items-center p-3 border border-gray-200 rounded-md hover:bg-gray-50">
                <svg className="w-5 h-5 text-gray-400 mr-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                </svg>
                <div>
                  <h3 className="text-sm font-medium text-gray-800">Industry Whitepapers</h3>
                  <p className="text-xs text-gray-500">In-depth analysis and research</p>
                </div>
              </a>
            </div>
          </div>
        </div>
      </div>
      
      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200 mt-2">
        <h2 className="text-xl font-medium text-gray-800 mb-4">Our Solutions</h2>
        
        <div className="space-y-6">
          <div className="border-b border-gray-200 pb-6">
            <h3 className="text-lg font-medium text-gray-800 mb-3">Energy Efficiency Optimization</h3>
            <div className="grid md:grid-cols-3 gap-6">
              <div className="md:col-span-2">
                <p className="text-gray-600 mb-4">
                  Our energy efficiency solutions identify and eliminate waste throughout your industrial processes.
                  Through detailed energy audits, advanced monitoring systems, and strategic upgrades, we help you
                  minimize consumption while maintaining or improving production output.
                </p>
                <ul className="space-y-2">
                  <li className="flex items-start">
                    <svg className="h-5 w-5 text-green-500 mr-2 mt-0.5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span className="text-gray-600">Comprehensive energy audits and monitoring</span>
                  </li>
                  <li className="flex items-start">
                    <svg className="h-5 w-5 text-green-500 mr-2 mt-0.5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span className="text-gray-600">Equipment upgrades and optimization</span>
                  </li>
                  <li className="flex items-start">
                    <svg className="h-5 w-5 text-green-500 mr-2 mt-0.5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span className="text-gray-600">Process optimization and heat recovery</span>
                  </li>
                </ul>
              </div>
              <div className="bg-gray-100 rounded-md p-4 flex items-center justify-center">
                <div className="text-center">
                  <p className="text-gray-500">Average Client Savings</p>
                  <p className="text-3xl font-bold text-green-600">22%</p>
                  <p className="text-sm text-gray-500">Energy Reduction</p>
                </div>
              </div>
            </div>
          </div>
          
          <div className="border-b border-gray-200 pb-6">
            <h3 className="text-lg font-medium text-gray-800 mb-3">Renewable Energy Integration</h3>
            <div className="grid md:grid-cols-3 gap-6">
              <div className="md:col-span-2">
                <p className="text-gray-600 mb-4">
                  Transition to cleaner energy sources with our renewable integration solutions. We design and implement
                  solar, wind, biomass, and other renewable systems that reduce your carbon footprint while providing
                  reliable energy for your operations.
                </p>
                <ul className="space-y-2">
                  <li className="flex items-start">
                    <svg className="h-5 w-5 text-green-500 mr-2 mt-0.5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span className="text-gray-600">On-site renewable energy generation</span>
                  </li>
                  <li className="flex items-start">
                    <svg className="h-5 w-5 text-green-500 mr-2 mt-0.5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span className="text-gray-600">Power Purchase Agreements (PPAs)</span>
                  </li>
                  <li className="flex items-start">
                    <svg className="h-5 w-5 text-green-500 mr-2 mt-0.5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span className="text-gray-600">Energy storage solutions</span>
                  </li>
                </ul>
              </div>
              <div className="bg-gray-100 rounded-md p-4 flex items-center justify-center">
                <div className="text-center">
                  <p className="text-gray-500">Average Client Result</p>
                  <p className="text-3xl font-bold text-green-600">40%</p>
                  <p className="text-sm text-gray-500">Carbon Reduction</p>
                </div>
              </div>
            </div>
          </div>
          
          <div>
            <h3 className="text-lg font-medium text-gray-800 mb-3">Smart Energy Management Systems</h3>
            <div className="grid md:grid-cols-3 gap-6">
              <div className="md:col-span-2">
                <p className="text-gray-600 mb-4">
                  Our intelligent energy management platforms provide real-time monitoring, analytics, and automated
                  control of your energy systems. These solutions help you optimize consumption, predict maintenance
                  needs, and make data-driven decisions about your energy strategy.
                </p>
                <ul className="space-y-2">
                  <li className="flex items-start">
                    <svg className="h-5 w-5 text-green-500 mr-2 mt-0.5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span className="text-gray-600">IoT-enabled energy monitoring</span>
                  </li>
                  <li className="flex items-start">
                    <svg className="h-5 w-5 text-green-500 mr-2 mt-0.5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span className="text-gray-600">AI-powered optimization algorithms</span>
                  </li>
                  <li className="flex items-start">
                    <svg className="h-5 w-5 text-green-500 mr-2 mt-0.5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span className="text-gray-600">Predictive maintenance</span>
                  </li>
                </ul>
              </div>
              <div className="bg-gray-100 rounded-md p-4 flex items-center justify-center">
                <div className="text-center">
                  <p className="text-gray-500">Average Client Result</p>
                  <p className="text-3xl font-bold text-green-600">15%</p>
                  <p className="text-sm text-gray-500">Enhanced Efficiency</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div className="bg-green-50 p-6 rounded-lg border border-green-200 mt-2 flex flex-col md:flex-row md:items-center justify-between">
        <div>
          <h2 className="text-xl font-medium text-green-800 mb-2">Ready to optimize your industrial energy usage?</h2>
          <p className="text-green-700">Our experts can help you develop a tailored solution for your specific needs.</p>
        </div>
        <div className="mt-4 md:mt-0">
          <button className="px-6 py-3 bg-green-600 text-white font-medium rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2">
            Contact an Expert
          </button>
        </div>
      </div>
    </div>
  );
} 