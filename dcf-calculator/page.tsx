import React from "react";

export default function DCFCalculatorPage() {
  return (
    <div className="flex flex-col gap-5">
      <h1 className="text-3xl font-bold text-gray-800">DCF Calculator</h1>
      <p className="text-gray-600">Calculate Discounted Cash Flow and ROI for energy projects</p>
      
      <div className="grid gap-6 lg:grid-cols-3">
        <div className="lg:col-span-2 bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h2 className="text-xl font-medium text-gray-800 mb-4">Project Information</h2>
          
          <div className="space-y-5">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label htmlFor="project-name" className="block text-sm font-medium text-gray-700 mb-1">
                  Project Name
                </label>
                <input
                  type="text"
                  id="project-name"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
                  placeholder="Enter project name"
                />
              </div>
              
              <div>
                <label htmlFor="industry" className="block text-sm font-medium text-gray-700 mb-1">
                  Industry
                </label>
                <select
                  id="industry"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
                >
                  <option value="">Select industry</option>
                  <option value="manufacturing">Manufacturing</option>
                  <option value="commercial">Commercial Real Estate</option>
                  <option value="residential">Residential Real Estate</option>
                  <option value="industrial">Industrial</option>
                  <option value="healthcare">Healthcare</option>
                  <option value="education">Education</option>
                </select>
              </div>
            </div>
            
            <div>
              <label htmlFor="investment" className="block text-sm font-medium text-gray-700 mb-1">
                Initial Investment Amount ($)
              </label>
              <input
                type="number"
                id="investment"
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
                placeholder="Enter amount"
              />
            </div>
            
            <div>
              <div className="flex justify-between">
                <label htmlFor="project-duration" className="block text-sm font-medium text-gray-700">
                  Project Duration (Years)
                </label>
                <span className="text-sm text-gray-500">10 years</span>
              </div>
              <input
                type="range"
                id="project-duration"
                min="5"
                max="30"
                step="1"
                defaultValue="10"
                className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer mt-2"
              />
            </div>
            
            <div>
              <div className="flex justify-between">
                <label htmlFor="discount-rate" className="block text-sm font-medium text-gray-700">
                  Discount Rate (%)
                </label>
                <span className="text-sm text-gray-500">8.0%</span>
              </div>
              <input
                type="range"
                id="discount-rate"
                min="5"
                max="15"
                step="0.5"
                defaultValue="8"
                className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer mt-2"
              />
            </div>
            
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">
                Projected Annual Cash Flows ($)
              </label>
              <div className="grid grid-cols-2 gap-3">
                {Array.from({ length: 10 }).map((_, index) => (
                  <div key={index} className="flex items-center">
                    <span className="text-sm text-gray-500 w-16">Year {index + 1}</span>
                    <input
                      type="number"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
                      placeholder="Cash flow"
                    />
                  </div>
                ))}
              </div>
            </div>
            
            <button
              type="button"
              className="w-full py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
            >
              Calculate ROI
            </button>
          </div>
        </div>
        
        <div className="space-y-6">
          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <h2 className="text-xl font-medium text-gray-800 mb-4">ROI Analysis</h2>
            <div className="h-60 flex items-center justify-center border-2 border-dashed border-gray-200 rounded-md">
              <p className="text-gray-500">Chart will appear here</p>
            </div>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <h2 className="text-xl font-medium text-gray-800 mb-4">Project Viability</h2>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="bg-gray-100 p-4 rounded-md">
                <p className="text-sm text-gray-500">Project ROI</p>
                <p className="text-2xl font-bold text-green-600">0.0%</p>
              </div>
              <div className="bg-gray-100 p-4 rounded-md">
                <p className="text-sm text-gray-500">Target ROI</p>
                <p className="text-2xl font-bold">9.0%</p>
              </div>
              <div className="bg-gray-100 p-4 rounded-md">
                <p className="text-sm text-gray-500">NPV</p>
                <p className="text-2xl font-bold">$0</p>
              </div>
              <div className="bg-gray-100 p-4 rounded-md">
                <p className="text-sm text-gray-500">Payback Period</p>
                <p className="text-2xl font-bold">0.0 years</p>
              </div>
            </div>
            
            <div className="mt-6">
              <div className="border border-gray-200 rounded-md p-4">
                <h3 className="text-sm font-medium text-gray-700 mb-2">Project Status</h3>
                <div className="flex items-center">
                  <div className="w-3 h-3 rounded-full bg-gray-300 mr-2"></div>
                  <p className="text-sm text-gray-500">Not calculated</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 