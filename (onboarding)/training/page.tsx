export default function TrainingPage() {
  return (
    <div className="flex flex-col gap-5">
      <h1 className="text-3xl font-bold text-gray-800">Sales Onboarding & Training</h1>
      <p className="text-gray-600">Welcome to the GETEC Energy Services sales team onboarding program</p>
      
      <div className="flex border-b border-gray-200 mb-6">
        <button className="px-4 py-2 text-sm font-medium text-green-600 border-b-2 border-green-600">
          Getting Started
        </button>
        <button className="px-4 py-2 text-sm font-medium text-gray-500 hover:text-gray-700">
          Sales Process
        </button>
        <button className="px-4 py-2 text-sm font-medium text-gray-500 hover:text-gray-700">
          Product Knowledge
        </button>
        <button className="px-4 py-2 text-sm font-medium text-gray-500 hover:text-gray-700">
          Resources
        </button>
      </div>
      
      <div className="grid gap-6 lg:grid-cols-3">
        <div className="lg:col-span-2 space-y-6">
          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <h2 className="text-xl font-medium text-gray-800 mb-2">Welcome to GETEC Energy Services</h2>
            <p className="text-gray-600 mb-4">
              As a new member of our sales team, you'll be helping clients optimize their energy consumption,
              reduce costs, and increase sustainability. This onboarding program will provide you with
              the knowledge and tools you need to succeed.
            </p>
            
            <div className="aspect-video bg-gray-100 rounded-md mb-4 flex items-center justify-center">
              <p className="text-gray-400">Welcome Video</p>
            </div>
            
            <div className="p-4 bg-yellow-50 rounded-md">
              <h3 className="text-md font-medium text-yellow-800 mb-2">Important Note</h3>
              <p className="text-sm text-yellow-700">
                All sales proposals must meet our minimum ROI threshold of 9% to be considered viable.
                Use the DCF calculator tool to ensure your proposals meet this requirement.
              </p>
            </div>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <h2 className="text-xl font-medium text-gray-800 mb-4">Onboarding Checklist</h2>
            
            <div className="space-y-3">
              <div className="flex items-start">
                <div className="flex items-center h-5">
                  <input
                    id="checklist-1"
                    type="checkbox"
                    className="focus:ring-green-500 h-4 w-4 text-green-600 border-gray-300 rounded"
                  />
                </div>
                <div className="ml-3 text-sm">
                  <label htmlFor="checklist-1" className="font-medium text-gray-700">Complete company introduction</label>
                  <p className="text-gray-500">Learn about GETEC's history, mission, and values</p>
                </div>
              </div>
              
              <div className="flex items-start">
                <div className="flex items-center h-5">
                  <input
                    id="checklist-2"
                    type="checkbox"
                    className="focus:ring-green-500 h-4 w-4 text-green-600 border-gray-300 rounded"
                  />
                </div>
                <div className="ml-3 text-sm">
                  <label htmlFor="checklist-2" className="font-medium text-gray-700">Review product offerings</label>
                  <p className="text-gray-500">Understand our industry and real estate energy solutions</p>
                </div>
              </div>
              
              <div className="flex items-start">
                <div className="flex items-center h-5">
                  <input
                    id="checklist-3"
                    type="checkbox"
                    className="focus:ring-green-500 h-4 w-4 text-green-600 border-gray-300 rounded"
                  />
                </div>
                <div className="ml-3 text-sm">
                  <label htmlFor="checklist-3" className="font-medium text-gray-700">Learn the sales process</label>
                  <p className="text-gray-500">Understand our standardized approach to client engagement</p>
                </div>
              </div>
              
              <div className="flex items-start">
                <div className="flex items-center h-5">
                  <input
                    id="checklist-4"
                    type="checkbox"
                    className="focus:ring-green-500 h-4 w-4 text-green-600 border-gray-300 rounded"
                  />
                </div>
                <div className="ml-3 text-sm">
                  <label htmlFor="checklist-4" className="font-medium text-gray-700">Practice using sales tools</label>
                  <p className="text-gray-500">Get familiar with the DCF calculator and proposal generator</p>
                </div>
              </div>
              
              <div className="flex items-start">
                <div className="flex items-center h-5">
                  <input
                    id="checklist-5"
                    type="checkbox"
                    className="focus:ring-green-500 h-4 w-4 text-green-600 border-gray-300 rounded"
                  />
                </div>
                <div className="ml-3 text-sm">
                  <label htmlFor="checklist-5" className="font-medium text-gray-700">Shadow experienced sales team member</label>
                  <p className="text-gray-500">Participate in client meetings with a mentor</p>
                </div>
              </div>
            </div>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <h2 className="text-xl font-medium text-gray-800 mb-4">The GETEC Sales Process</h2>
            
            <div className="space-y-6">
              <div className="relative">
                <div className="absolute inset-0 flex items-center" aria-hidden="true">
                  <div className="w-full border-t border-gray-300"></div>
                </div>
                <div className="relative flex justify-center">
                  <span className="px-2 bg-white text-sm text-gray-500">Step 1</span>
                </div>
              </div>
              
              <div className="p-4 border border-gray-200 rounded-md">
                <h3 className="text-lg font-medium text-gray-800 mb-2">Client Prospecting & Screening</h3>
                <p className="text-gray-600 mb-2">
                  Identify potential clients based on industry, energy usage, and potential for optimization.
                  Research their current operations and pain points.
                </p>
                <div className="flex items-center text-sm text-green-600">
                  <svg className="w-5 h-5 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  Use our screening template to qualify leads
                </div>
              </div>
              
              <div className="relative">
                <div className="absolute inset-0 flex items-center" aria-hidden="true">
                  <div className="w-full border-t border-gray-300"></div>
                </div>
                <div className="relative flex justify-center">
                  <span className="px-2 bg-white text-sm text-gray-500">Step 2</span>
                </div>
              </div>
              
              <div className="p-4 border border-gray-200 rounded-md">
                <h3 className="text-lg font-medium text-gray-800 mb-2">Needs Assessment</h3>
                <p className="text-gray-600 mb-2">
                  Meet with the client to understand their specific energy challenges, goals,
                  and constraints. Collect data on current energy usage.
                </p>
                <div className="flex items-center text-sm text-green-600">
                  <svg className="w-5 h-5 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  Use the needs assessment questionnaire
                </div>
              </div>
            </div>
            
            <div className="mt-4">
              <a href="/process" className="text-green-600 hover:text-green-700 text-sm font-medium flex items-center">
                View all sales process steps
                <svg className="w-5 h-5 ml-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </a>
            </div>
          </div>
        </div>
        
        <div className="space-y-6">
          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <h2 className="text-xl font-medium text-gray-800 mb-4">Your Onboarding Progress</h2>
            
            <div className="mb-4">
              <div className="flex justify-between mb-1">
                <span className="text-sm font-medium text-gray-700">Progress</span>
                <span className="text-sm font-medium text-gray-700">25%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2.5">
                <div className="bg-green-600 h-2.5 rounded-full w-1/4"></div>
              </div>
            </div>
            
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-gray-500">Training modules completed</span>
                <span className="text-sm font-medium">2/8</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-500">Practice exercises completed</span>
                <span className="text-sm font-medium">1/5</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-500">Days until completion</span>
                <span className="text-sm font-medium">12</span>
              </div>
            </div>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <h2 className="text-xl font-medium text-gray-800 mb-4">Learning Resources</h2>
            
            <div className="space-y-4">
              <a href="#" className="flex items-center p-3 border border-gray-200 rounded-md hover:bg-gray-50">
                <svg className="w-5 h-5 text-gray-400 mr-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                </svg>
                <div>
                  <h3 className="text-sm font-medium text-gray-800">Sales Playbook</h3>
                  <p className="text-xs text-gray-500">Comprehensive guide to our sales methodology</p>
                </div>
              </a>
              
              <a href="#" className="flex items-center p-3 border border-gray-200 rounded-md hover:bg-gray-50">
                <svg className="w-5 h-5 text-gray-400 mr-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 4v16M17 4v16M3 8h4m10 0h4M3 12h18M3 16h4m10 0h4M4 20h16a1 1 0 001-1V5a1 1 0 00-1-1H4a1 1 0 00-1 1v14a1 1 0 001 1z" />
                </svg>
                <div>
                  <h3 className="text-sm font-medium text-gray-800">Video Tutorials</h3>
                  <p className="text-xs text-gray-500">Step-by-step guides for using our tools</p>
                </div>
              </a>
              
              <a href="#" className="flex items-center p-3 border border-gray-200 rounded-md hover:bg-gray-50">
                <svg className="w-5 h-5 text-gray-400 mr-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <div>
                  <h3 className="text-sm font-medium text-gray-800">Case Studies</h3>
                  <p className="text-xs text-gray-500">Real-world examples of successful projects</p>
                </div>
              </a>
              
              <a href="#" className="flex items-center p-3 border border-gray-200 rounded-md hover:bg-gray-50">
                <svg className="w-5 h-5 text-gray-400 mr-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <div>
                  <h3 className="text-sm font-medium text-gray-800">FAQ</h3>
                  <p className="text-xs text-gray-500">Common questions and answers</p>
                </div>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 