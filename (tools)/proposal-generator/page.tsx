export default function ProposalGeneratorPage() {
  return (
    <div className="flex flex-col gap-5">
      <h1 className="text-3xl font-bold text-gray-800">Proposal Generator</h1>
      <p className="text-gray-600">Create professional sales proposals with standardized templates</p>
      
      <div className="flex border-b border-gray-200">
        <button className="px-4 py-2 text-sm font-medium text-green-600 border-b-2 border-green-600">
          Editor
        </button>
        <button className="px-4 py-2 text-sm font-medium text-gray-500 hover:text-gray-700">
          Templates
        </button>
        <button className="px-4 py-2 text-sm font-medium text-gray-500 hover:text-gray-700">
          Saved Proposals
        </button>
      </div>
      
      <div className="grid gap-6 lg:grid-cols-3">
        <div className="lg:col-span-2 bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h2 className="text-xl font-medium text-gray-800 mb-4">Proposal Editor</h2>
          
          <div className="space-y-5">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label htmlFor="client-name" className="block text-sm font-medium text-gray-700 mb-1">
                  Client Name
                </label>
                <input
                  type="text"
                  id="client-name"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
                  placeholder="Enter client name"
                />
              </div>
              
              <div>
                <label htmlFor="project-title" className="block text-sm font-medium text-gray-700 mb-1">
                  Project Title
                </label>
                <input
                  type="text"
                  id="project-title"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
                  placeholder="Enter project title"
                />
              </div>
            </div>
            
            <div>
              <label htmlFor="project-description" className="block text-sm font-medium text-gray-700 mb-1">
                Project Description
              </label>
              <textarea
                id="project-description"
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
                placeholder="Describe the project"
              ></textarea>
            </div>
            
            <div>
              <label htmlFor="solution" className="block text-sm font-medium text-gray-700 mb-1">
                Proposed Solution
              </label>
              <textarea
                id="solution"
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
                placeholder="Describe your solution"
              ></textarea>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label htmlFor="investment" className="block text-sm font-medium text-gray-700 mb-1">
                  Investment Required
                </label>
                <input
                  type="text"
                  id="investment"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
                  placeholder="Enter amount"
                />
              </div>
              
              <div>
                <label htmlFor="roi" className="block text-sm font-medium text-gray-700 mb-1">
                  Expected ROI
                </label>
                <input
                  type="text"
                  id="roi"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
                  placeholder="Enter ROI percentage"
                />
              </div>
            </div>
            
            <div>
              <label htmlFor="timeline" className="block text-sm font-medium text-gray-700 mb-1">
                Implementation Timeline
              </label>
              <textarea
                id="timeline"
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
                placeholder="Outline the implementation timeline"
              ></textarea>
            </div>
            
            <div className="flex gap-3">
              <button
                type="button"
                className="py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
              >
                Save Draft
              </button>
              <button
                type="button"
                className="py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
              >
                Preview
              </button>
            </div>
          </div>
        </div>
        
        <div className="space-y-6">
          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <h2 className="text-xl font-medium text-gray-800 mb-4">Preview</h2>
            <div className="aspect-[8.5/11] bg-white border shadow-sm rounded-md flex items-center justify-center">
              <p className="text-gray-400">Proposal preview will appear here</p>
            </div>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <h2 className="text-xl font-medium text-gray-800 mb-4">Actions</h2>
            <div className="space-y-3">
              <button
                type="button"
                className="w-full py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Export as PDF
              </button>
              <button
                type="button"
                className="w-full py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
              >
                Send to Client
              </button>
              <button
                type="button"
                className="w-full py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
              >
                Share with Team
              </button>
            </div>
          </div>
        </div>
      </div>
      
      {/* Template Section (Hidden by default) */}
      <div className="hidden mt-6">
        <h2 className="text-xl font-medium text-gray-800 mb-4">Template Gallery</h2>
        <p className="text-gray-600 mb-4">Choose from pre-designed proposal templates</p>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array.from({ length: 6 }).map((_, index) => (
            <div key={index} className="border rounded-lg overflow-hidden hover:shadow-md transition-shadow cursor-pointer">
              <div className="aspect-video bg-gray-100 flex items-center justify-center">
                <p className="text-gray-400">Template Preview</p>
              </div>
              <div className="p-4">
                <span className="text-xs font-medium text-blue-600 bg-blue-50 px-2 py-1 rounded-full">
                  {["Industry", "Real Estate", "Commercial", "Healthcare", "General", "Custom"][index]}
                </span>
                <h3 className="mt-2 font-medium">Template {index + 1}</h3>
                <p className="text-sm text-gray-500 mt-1">Template description goes here</p>
                <button className="mt-3 text-sm text-white bg-green-600 px-3 py-1.5 rounded hover:bg-green-700 w-full">
                  Use Template
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>
      
      {/* Saved Proposals Section (Hidden by default) */}
      <div className="hidden mt-6">
        <h2 className="text-xl font-medium text-gray-800 mb-4">Saved Proposals</h2>
        <p className="text-gray-600 mb-4">Access your saved and sent proposals</p>
        
        <div className="border rounded-md divide-y">
          {Array.from({ length: 5 }).map((_, index) => (
            <div key={index} className="p-4 flex justify-between items-center hover:bg-gray-50 cursor-pointer">
              <div>
                <h3 className="font-medium">Proposal {index + 1}</h3>
                <p className="text-sm text-gray-500">Created: {new Date().toLocaleDateString()} • Status: {["Draft", "Sent", "Approved", "Rejected", "In Progress"][index]}</p>
              </div>
              <div className="flex gap-2">
                <button className="p-1 text-blue-600 hover:text-blue-800">Edit</button>
                <button className="p-1 text-gray-600 hover:text-gray-800">Duplicate</button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
} 