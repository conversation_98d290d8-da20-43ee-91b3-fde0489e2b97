import React from "react";

export default function Dashboard() {
  return (
    <div className="flex flex-col gap-5">
      <h1 className="text-3xl font-bold text-gray-800">Sales Operations Dashboard</h1>
      
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <h2 className="text-sm font-medium text-gray-500">Active Proposals</h2>
            <div className="p-2 bg-green-100 rounded-full">
              <svg className="w-5 h-5 text-green-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
          </div>
          <div className="mt-4">
            <span className="text-2xl font-bold">24</span>
            <span className="ml-2 text-sm font-medium text-green-600">+12%</span>
          </div>
          <p className="mt-1 text-sm text-gray-500">from last month</p>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <h2 className="text-sm font-medium text-gray-500">Conversion Rate</h2>
            <div className="p-2 bg-blue-100 rounded-full">
              <svg className="w-5 h-5 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
              </svg>
            </div>
          </div>
          <div className="mt-4">
            <span className="text-2xl font-bold">32%</span>
            <span className="ml-2 text-sm font-medium text-green-600">+2%</span>
          </div>
          <p className="mt-1 text-sm text-gray-500">from last month</p>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <h2 className="text-sm font-medium text-gray-500">Projects Above 9% ROI</h2>
            <div className="p-2 bg-yellow-100 rounded-full">
              <svg className="w-5 h-5 text-yellow-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 3.055A9.001 9.001 0 1020.945 13H11V3.055z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20.488 9H15V3.512A9.025 9.025 0 0120.488 9z" />
              </svg>
            </div>
          </div>
          <div className="mt-4">
            <span className="text-2xl font-bold">18</span>
            <span className="ml-2 text-sm font-medium text-green-600">+5</span>
          </div>
          <p className="mt-1 text-sm text-gray-500">from last month</p>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <h2 className="text-sm font-medium text-gray-500">Sales Team Activity</h2>
            <div className="p-2 bg-purple-100 rounded-full">
              <svg className="w-5 h-5 text-purple-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
            </div>
          </div>
          <div className="mt-4">
            <span className="text-2xl font-bold">85%</span>
            <span className="ml-2 text-sm font-medium text-green-600">+3%</span>
          </div>
          <p className="mt-1 text-sm text-gray-500">from last month</p>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h2 className="text-lg font-medium text-gray-800">Quick Actions</h2>
          <p className="mt-1 text-sm text-gray-500">Access frequently used tools</p>
          
          <div className="mt-6 space-y-3">
            <a href="/dcf-calculator" className="flex items-center py-3 px-4 bg-green-50 hover:bg-green-100 text-green-700 rounded-md">
              <svg className="w-5 h-5 mr-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
              </svg>
              New DCF Calculation
            </a>
            
            <a href="/proposal-generator" className="flex items-center py-3 px-4 bg-blue-50 hover:bg-blue-100 text-blue-700 rounded-md">
              <svg className="w-5 h-5 mr-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              Create Sales Proposal
            </a>
            
            <a href="/industry" className="flex items-center py-3 px-4 bg-yellow-50 hover:bg-yellow-100 text-yellow-700 rounded-md">
              <svg className="w-5 h-5 mr-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
              </svg>
              Industry Solutions Lookup
            </a>
            
            <a href="/real-estate" className="flex items-center py-3 px-4 bg-purple-50 hover:bg-purple-100 text-purple-700 rounded-md">
              <svg className="w-5 h-5 mr-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
              </svg>
              Real Estate Solutions Guide
            </a>
          </div>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h2 className="text-lg font-medium text-gray-800">Active Projects</h2>
          <p className="mt-1 text-sm text-gray-500">Track ongoing project progress</p>
          
          <div className="mt-4 flex border-b">
            <button className="px-4 py-2 text-sm font-medium text-green-600 border-b-2 border-green-600">
              Industry
            </button>
            <button className="px-4 py-2 text-sm font-medium text-gray-500 hover:text-gray-700">
              Real Estate
            </button>
          </div>
          
          <div className="mt-4 space-y-3">
            <div className="p-3 border rounded-md flex justify-between items-center hover:bg-gray-50">
              <div>
                <h3 className="font-medium">Automotive Plant Retrofit</h3>
                <p className="text-xs text-gray-500">Last updated: 3 days ago</p>
              </div>
              <span className="text-green-600 font-medium">12.4% ROI</span>
            </div>
            
            <div className="p-3 border rounded-md flex justify-between items-center hover:bg-gray-50">
              <div>
                <h3 className="font-medium">Chemical Plant Optimization</h3>
                <p className="text-xs text-gray-500">Last updated: 1 week ago</p>
              </div>
              <span className="text-green-600 font-medium">9.8% ROI</span>
            </div>
            
            <div className="p-3 border rounded-md flex justify-between items-center hover:bg-gray-50">
              <div>
                <h3 className="font-medium">Manufacturing Facility</h3>
                <p className="text-xs text-gray-500">Last updated: 2 weeks ago</p>
              </div>
              <span className="text-yellow-600 font-medium">8.7% ROI</span>
            </div>
            
            <a href="#" className="block text-center text-sm text-blue-600 hover:text-blue-800 mt-2">
              View all projects
            </a>
          </div>
        </div>
      </div>
    </div>
  );
} 