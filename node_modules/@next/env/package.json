{"name": "@next/env", "version": "14.1.0", "keywords": ["react", "next", "next.js", "dotenv"], "description": "Next.js dotenv file loading", "repository": {"type": "git", "url": "https://github.com/vercel/next.js", "directory": "packages/next-env"}, "author": "Next.js Team <<EMAIL>>", "license": "MIT", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"dev": "ncc build ./index.ts -w -o dist/", "prerelease": "node ../../scripts/rm.mjs dist", "types": "tsc index.ts --declaration --emitDeclarationOnly --declarationDir dist --esModuleInterop", "release": "ncc build ./index.ts -o ./dist/ --minify --no-cache --no-source-map-register", "build": "pnpm release && pnpm types", "prepublishOnly": "cd ../../ && turbo run build"}, "devDependencies": {"@vercel/ncc": "0.34.0", "dotenv": "16.3.1", "dotenv-expand": "10.0.0"}}