<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GETEC Sales Operations Platform</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
        .getec-green { color: #16a34a; }
        .bg-getec-green { background-color: #16a34a; }
        .hover\:bg-getec-green-dark:hover { background-color: #15803d; }
        .border-getec-green { border-color: #16a34a; }
        .ring-getec-green { --tw-ring-color: #16a34a; }
    </style>
</head>
<body class="bg-gray-50">
    <div class="flex min-h-screen">
        <!-- Sidebar -->
        <aside class="hidden md:flex md:w-64 md:flex-col">
            <div class="flex flex-col flex-grow pt-5 bg-white border-r border-gray-200">
                <div class="flex items-center justify-center h-16 flex-shrink-0 px-4">
                    <h1 class="text-xl font-semibold getec-green">GETEC Sales</h1>
                </div>
                <div class="flex flex-col flex-grow mt-5">
                    <nav class="flex-1 px-2 pb-4 space-y-1">
                        <a href="#" class="flex items-center px-2 py-2 text-sm font-medium text-gray-900 rounded-md bg-gray-100">
                            <svg class="w-5 h-5 mr-3 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                            </svg>
                            Dashboard
                        </a>
                        <a href="#dcf" class="flex items-center px-2 py-2 text-sm font-medium text-gray-600 rounded-md hover:bg-gray-50 hover:text-gray-900">
                            <svg class="w-5 h-5 mr-3 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                            </svg>
                            DCF Calculator
                        </a>
                        <a href="#proposal" class="flex items-center px-2 py-2 text-sm font-medium text-gray-600 rounded-md hover:bg-gray-50 hover:text-gray-900">
                            <svg class="w-5 h-5 mr-3 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            Proposal Generator
                        </a>
                        <a href="#training" class="flex items-center px-2 py-2 text-sm font-medium text-gray-600 rounded-md hover:bg-gray-50 hover:text-gray-900">
                            <svg class="w-5 h-5 mr-3 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                            </svg>
                            Onboarding & Training
                        </a>
                        <a href="#industry" class="flex items-center px-2 py-2 text-sm font-medium text-gray-600 rounded-md hover:bg-gray-50 hover:text-gray-900">
                            <svg class="w-5 h-5 mr-3 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                            </svg>
                            Industry Solutions
                        </a>
                        <a href="#realestate" class="flex items-center px-2 py-2 text-sm font-medium text-gray-600 rounded-md hover:bg-gray-50 hover:text-gray-900">
                            <svg class="w-5 h-5 mr-3 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                            </svg>
                            Real Estate Solutions
                        </a>
                    </nav>
                </div>
            </div>
        </aside>

        <!-- Main Content -->
        <div class="flex flex-col flex-1">
            <!-- Header -->
            <header class="sticky top-0 z-10 flex h-16 flex-shrink-0 bg-white shadow">
                <div class="flex flex-1 justify-between px-4">
                    <div class="flex flex-1">
                        <div class="flex w-full md:ml-0">
                            <div class="flex items-center w-full text-gray-400 focus-within:text-gray-600">
                                <span class="text-lg font-medium getec-green md:hidden">GETEC Sales</span>
                            </div>
                        </div>
                    </div>
                    <div class="ml-4 flex items-center md:ml-6">
                        <button type="button" class="rounded-full bg-white p-1 text-gray-400 hover:text-gray-500">
                            <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"></path>
                            </svg>
                        </button>
                        <div class="ml-3 relative">
                            <button type="button" class="flex max-w-xs items-center rounded-full bg-white text-sm">
                                <span class="inline-flex h-8 w-8 items-center justify-center rounded-full bg-gray-500">
                                    <span class="text-sm font-medium leading-none text-white">JD</span>
                                </span>
                            </button>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Main Content Area -->
            <main class="flex-1">
                <div class="py-6">
                    <div class="mx-auto max-w-7xl px-4 sm:px-6 md:px-8">
                        <!-- Dashboard Content -->
                        <div id="dashboard" class="p-8">
                            <h1 class="text-3xl font-bold text-gray-800 mb-4">GETEC Sales Operations Platform</h1>
                            <p class="text-gray-600 mb-8">A standardized sales operating model for GETEC Energy Services</p>
                            
                            <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                                <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                                    <h2 class="text-xl font-medium text-gray-800 mb-2">DCF Calculator</h2>
                                    <p class="text-gray-600 mb-4">Calculate ROI for energy projects with minimum 9% threshold</p>
                                    <a href="#dcf" class="getec-green font-medium">Access tool →</a>
                                </div>
                                
                                <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                                    <h2 class="text-xl font-medium text-gray-800 mb-2">Proposal Generator</h2>
                                    <p class="text-gray-600 mb-4">Create professional sales proposals with templates</p>
                                    <a href="#proposal" class="getec-green font-medium">Access tool →</a>
                                </div>
                                
                                <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                                    <h2 class="text-xl font-medium text-gray-800 mb-2">Sales Onboarding</h2>
                                    <p class="text-gray-600 mb-4">Training resources and sales process documentation</p>
                                    <a href="#training" class="getec-green font-medium">Access training →</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
            
            <!-- Footer -->
            <footer class="bg-white border-t border-gray-200 py-4">
                <div class="mx-auto max-w-7xl px-4 sm:px-6 md:px-8">
                    <p class="text-center text-sm text-gray-500">© 2023 GETEC Energy Services. All rights reserved.</p>
                </div>
            </footer>
        </div>
    </div>
</body>
</html>
